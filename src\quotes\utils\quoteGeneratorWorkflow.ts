import { getQuoteClient } from '../../lib/supabaseManager';

// Interfaces following your exact specifications
export interface FamilyTypeDB {
  family_id: number;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;
  no_of_child: number;
  no_of_children: number;
  family_count: number;
  cab_type: string;
  cab_capacity: number;
  rooms_need: number;
}

export interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  total_cost: number;
  no_of_persons: number;
  extra_adults: number;
  children: number;
  infants: number;
  commission_rate?: number;
  discount_amount?: number;
  gst_rate?: number;
}

export interface HotelRow {
  hotelName: string;
  roomType: string;
  price: number;
  mealPlan: string;
  noOfRooms: number;
  stayNights: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  gstType: string;
  tacPercentage: number;
}

export interface QuoteMappingData {
  id: string;
  quote_id: string;
  quote_name: string;
  customer_name: string;
  destination: string;
  hotel_mappings: any[];
  vehicle_mappings: any[];
  additional_costs: {
    meal_cost_per_person: number;
    ferry_cost: number;
    activity_cost_per_person: number;
    guide_cost_per_day: number;
    parking_toll_multiplier: number;
  };
}

export interface RoomCalculationResult {
  roomsNeeded: number;
  extraAdults: number;
  childrenCharged: number;
  childrenFree: number;
  infantsFree: number;
  totalOccupancy: number;
  roomType: string;
  cabType: string;
  cabCapacity: number;
}

export interface HotelCostResult {
  baseRoomCost: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  totalHotelCost: number;
  gstAmount: number;
  tacAmount: number;
}

export interface PackageCostResult {
  familyType: FamilyTypeDB;
  roomCalculation: RoomCalculationResult;
  hotelCosts: HotelCostResult;
  additionalCosts: any;
  vehicleCosts: any;
  finalCalculation: {
    subtotal: number;
    discount: number;
    afterDiscount: number;
    commission: number;
    afterCommission: number;
    gst: number;
    grandTotal: number;
  };
}

// Phase 1: Fetch family types from TripXplo Quote DB
export const fetchFamilyTypesFromDB = async (): Promise<FamilyTypeDB[]> => {
  try {
    const supabase = getQuoteClient();
    const { data, error } = await supabase
      .from('family_type')
      .select(`
        family_id,
        family_type,
        no_of_adults,
        no_of_infants,
        no_of_child,
        no_of_children,
        family_count,
        cab_type,
        cab_capacity,
        rooms_need
      `)
      .order('family_id');

    if (error) {
      console.error('Error fetching family types:', error);
      return [];
    }

    console.log(`✅ Phase 1: Fetched ${data?.length || 0} family types from database`);
    return data || [];
  } catch (error) {
    console.error('Exception fetching family types:', error);
    return [];
  }
};

// Phase 2: Setup Family, Room Count & Cab Type following your exact specifications
export const calculateFamilyRoomAndCab = (familyType: FamilyTypeDB): RoomCalculationResult => {
  console.log(`\n🏨 Phase 2: Room & Cab calculation for ${familyType.family_type}`);
  
  const adults = familyType.no_of_adults;
  const childrenUnder5 = familyType.no_of_child; // Free
  const children6to12 = familyType.no_of_children; // Charged
  const infants = familyType.no_of_infants; // Free
  const familyCount = familyType.family_count;
  const roomsFromDB = familyType.rooms_need;

  console.log(`👥 Family composition:`, {
    adults,
    childrenUnder5,
    children6to12,
    infants,
    familyCount,
    roomsFromDB
  });

  // Hotel Room Calculation Steps (following your exact specifications):
  // 1 Hotel Room is for 2 Adults Occupancy & 2 Child below 5 yrs - Globally followed
  // (for Triple Room - 3, Family Room - 4)
  // 1 Extra Adult will be Allowed in Each Room
  // Eg: Deluxe Room (2 Adults + 1 Extra Adult), Family Room (4 Adults + 1 Extra Adults)
  // Infant & child <= 5 Free of Cost (no_of_infants, no_of_child)
  // Children > 6 & less than 12 Children Cost will be Calculated (no_of_children)

  let roomsNeeded = roomsFromDB; // Use database value as primary source
  let extraAdults = 0;
  let roomType = 'Standard';

  // Example 1: Dynamic Family Duo+ - 2 Adults + 2 Teenagers (Above 11 yrs)
  // DB Table Info: no_of_adults - 4, family_count - 4, rooms_need - 1
  // For this family we need 1 Family Room for 4 Adults because
  // Maximum we can accommodate 2 Adults + 1 Extra Adult in Standard or Deluxe Room

  if (roomsNeeded === 0) {
    if (adults <= 3) {
      roomsNeeded = 1;
      roomType = 'Standard/Deluxe';
    } else if (adults <= 5) {
      roomsNeeded = 1;
      roomType = 'Family Room';
    } else {
      // For more than 5 adults, need multiple rooms
      roomsNeeded = Math.ceil(adults / 3); // Max 3 adults per standard room
      roomType = 'Multiple Rooms';
    }
  }

  // Calculate extra adults beyond standard room capacity
  if (roomsNeeded === 1 && adults <= 5) {
    // Single Family Room: 4 adults standard + 1 extra = 5 max
    const standardCapacity = roomType === 'Family Room' ? 4 : 2;
    extraAdults = Math.max(0, adults - standardCapacity);
  } else {
    // Multiple Standard/Deluxe Rooms: 2 adults per room + 1 extra per room
    const standardCapacity = roomsNeeded * 2; // 2 adults per room standard
    extraAdults = Math.max(0, adults - standardCapacity);
  }

  // Example 2: Grand Family Nest - 2 Adults + 2 Child (Below 5 yrs) + 2 Grandparents
  // DB Table Info: no_of_adults - 4, family_count - 6, rooms_need - 2
  // Cab_type - Toyota Innova AC, cab_capacity - 6
  // For this family we need 2 Room for 4 Adults and 2 Child because
  // Maximum we can accommodate 3 Adult in Standard or Deluxe Room

  const result: RoomCalculationResult = {
    roomsNeeded,
    extraAdults,
    childrenCharged: children6to12, // Children 6-12 (charged)
    childrenFree: childrenUnder5, // Children ≤5 (free)
    infantsFree: infants, // Infants ≤2 (free)
    totalOccupancy: adults + children6to12 + childrenUnder5 + infants,
    roomType,
    cabType: familyType.cab_type,
    cabCapacity: familyType.cab_capacity
  };

  console.log(`✅ Room & Cab calculation result:`, result);
  return result;
};

// Phase 3: Hotel Configuration - Same as Quote Generator
export const calculateHotelCosts = (
  familyType: FamilyTypeDB,
  roomReq: RoomCalculationResult,
  hotelRows: HotelRow[],
  baselineQuote: BaselineQuote
): HotelCostResult => {
  console.log(`\n💰 Phase 3: Hotel cost calculation for ${familyType.family_type}`);
  
  // Room Cost = No of Nights * No of Rooms + Extra Adult + Children Cost + GST (GST Optional)
  
  let totalHotelCost = 0;
  let totalGstAmount = 0;
  let totalTacAmount = 0;
  let baseRoomCost = 0;
  let extraAdultCost = 0;
  let childrenCost = 0;
  let infantCost = 0;

  if (hotelRows.length === 0) {
    console.log(`⚠️ No hotel rows found, estimating from baseline`);
    // Estimate hotel cost as 60% of total cost
    const estimatedHotelCost = baselineQuote.total_cost * 0.6;
    const baselineRooms = Math.ceil((baselineQuote.no_of_persons + baselineQuote.extra_adults) / 2);
    const costPerRoom = estimatedHotelCost / baselineRooms;

    baseRoomCost = costPerRoom * roomReq.roomsNeeded;
    extraAdultCost = roomReq.extraAdults * 1000 * 3; // ₹1000 per extra adult per night, 3 nights
    childrenCost = roomReq.childrenCharged * 800 * 3; // ₹800 per child per night, 3 nights
    infantCost = roomReq.infantsFree * 0; // Infants free

    totalHotelCost = baseRoomCost + extraAdultCost + childrenCost + infantCost;
  } else {
    // Use actual hotel rows for calculation (following Quote Generator logic)
    hotelRows.forEach((row, index) => {
      console.log(`🏨 Processing hotel ${index + 1}: ${row.hotelName}`);

      // Base room price per night
      const roomNightPrice = row.price * roomReq.roomsNeeded * row.stayNights;
      baseRoomCost += roomNightPrice;

      // Extra costs: Extra adult cost, children cost, infant cost (per night)
      const extraAdultTotal = row.extraAdultCost * roomReq.extraAdults * row.stayNights;
      const childrenTotal = row.childrenCost * roomReq.childrenCharged * row.stayNights;
      const infantTotal = row.infantCost * roomReq.infantsFree * row.stayNights;

      extraAdultCost += extraAdultTotal;
      childrenCost += childrenTotal;
      infantCost += infantTotal;

      // Subtotal before GST and TAC
      const subtotal = roomNightPrice + extraAdultTotal + childrenTotal + infantTotal;

      // Tax settings: GST type and TAC percentage
      let gstAmount = 0;
      if (row.gstType !== 'NET' && row.gstType !== 'EXC') {
        gstAmount = (subtotal * Number(row.gstType)) / 100;
      }

      const tacAmount = (subtotal * row.tacPercentage) / 100;

      // Final price calculation based on GST type
      let finalPrice = subtotal + tacAmount;
      if (row.gstType !== 'EXC') {
        finalPrice += gstAmount;
      }

      totalHotelCost += finalPrice;
      totalGstAmount += gstAmount;
      totalTacAmount += tacAmount;

      console.log(`🏨 Hotel ${index + 1} costs:`, {
        roomNightPrice,
        extraAdultTotal,
        childrenTotal,
        infantTotal,
        subtotal,
        gstAmount,
        tacAmount,
        finalPrice
      });
    });
  }

  const result: HotelCostResult = {
    baseRoomCost,
    extraAdultCost,
    childrenCost,
    infantCost,
    totalHotelCost,
    gstAmount: totalGstAmount,
    tacAmount: totalTacAmount
  };

  console.log(`✅ Total hotel costs:`, result);
  return result;
};

// Phase 4: Additional Costs based on Quote Mapping
export const calculateAdditionalCosts = (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData,
  nights: number
) => {
  console.log(`\n🎯 Phase 4: Additional costs calculation for ${familyType.family_type}`);

  const familyCount = familyType.family_count;
  const additionalCosts = quoteMappingData.additional_costs;

  console.log(`👥 Family count: ${familyCount}`);
  console.log(`🌙 Nights: ${nights}`);
  console.log(`💰 Baseline total: ₹${baselineQuote.total_cost}`);

  // Basic Costs: Meals, transportation, cab sightseeing, train, ferry, parking/toll
  // Add-on Costs: Activities, marketing, additional services
  // Optional Costs: Flight tickets, guide wages

  // Per-person costs (scaled by family count)
  const meals = additionalCosts.meal_cost_per_person * familyCount;
  const activities = additionalCosts.activity_cost_per_person * familyCount;

  // Fixed costs
  const ferry = additionalCosts.ferry_cost;
  const guide = additionalCosts.guide_cost_per_day * nights;

  // Transportation costs (scaled from baseline)
  const baseTransportation = baselineQuote.total_cost * 0.25; // Assume 25% is transportation
  const scalingFactor = familyCount / Math.max((baselineQuote.no_of_persons + baselineQuote.extra_adults + baselineQuote.children + baselineQuote.infants), 1);
  const transportation = baseTransportation * scalingFactor;
  const cabSightseeing = baseTransportation * 0.3 * scalingFactor; // 30% of transportation for sightseeing

  // Percentage-based costs
  const parkingToll = baselineQuote.total_cost * 0.05 * additionalCosts.parking_toll_multiplier; // 5% of baseline for parking/toll

  const result = {
    meals,
    transportation,
    cabSightseeing,
    ferry,
    activities,
    guide,
    parkingToll,
    totalAdditionalCosts: meals + transportation + cabSightseeing + ferry + activities + guide + parkingToll
  };

  console.log(`💰 Additional costs breakdown:`, result);
  return result;
};

// Vehicle cost calculation based on family type
export const calculateVehicleCosts = (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData
) => {
  console.log(`\n🚗 Vehicle cost calculation for ${familyType.family_type}`);

  const cabType = familyType.cab_type.toLowerCase();
  const familyCount = familyType.family_count;

  console.log(`🚗 Required cab: ${familyType.cab_type} (capacity: ${familyType.cab_capacity})`);
  console.log(`👥 Family count: ${familyCount}`);

  // Find matching vehicle from mappings
  const vehicleMapping = quoteMappingData.vehicle_mappings.find(vm =>
    vm.vehicle_type.toLowerCase().includes(cabType.split(' ')[0]) &&
    vm.max_capacity >= familyCount
  );

  let vehicleType = familyType.cab_type;
  let multiplier = 1.0;
  let baseCost = baselineQuote.total_cost * 0.25; // Assume 25% is vehicle cost

  if (vehicleMapping) {
    vehicleType = vehicleMapping.vehicle_type;
    if (vehicleMapping.pricing_type === 'actual_cost') {
      baseCost = vehicleMapping.base_cost;
      multiplier = 1.0;
    } else {
      multiplier = vehicleMapping.cost_multiplier;
    }
    console.log(`✅ Found vehicle mapping: ${vehicleType} (multiplier: ${multiplier})`);
  } else {
    // Fallback multipliers based on capacity
    if (familyCount <= 4) {
      multiplier = 1.0; // Sedan
      vehicleType = 'Sedan/Dzire';
    } else if (familyCount <= 7) {
      multiplier = 1.2; // Innova
      vehicleType = 'Toyota Innova AC';
    } else if (familyCount <= 12) {
      multiplier = 1.4; // Tempo Traveller
      vehicleType = 'Tempo Traveller';
    } else {
      multiplier = 1.6; // Mini Bus
      vehicleType = 'Mini Bus';
    }
    console.log(`⚠️ No vehicle mapping found, using fallback: ${vehicleType} (multiplier: ${multiplier})`);
  }

  const result = {
    vehicleType,
    baseCost,
    multiplier,
    totalVehicleCost: baseCost * multiplier
  };

  console.log(`🚗 Vehicle costs:`, result);
  return result;
};

// Phase 5: Final Calculations - Same as Quote Generator
export const calculateFinalCosts = (
  hotelCosts: HotelCostResult,
  additionalCosts: any,
  vehicleCosts: any,
  baselineQuote: BaselineQuote
) => {
  console.log(`\n💰 Phase 5: Final calculations`);

  const subtotal = hotelCosts.totalHotelCost + additionalCosts.totalAdditionalCosts + vehicleCosts.totalVehicleCost;
  const discount = baselineQuote.discount_amount || 0;
  const afterDiscount = Math.max(0, subtotal - discount);

  const commissionRate = baselineQuote.commission_rate || 5;
  const commission = afterDiscount * (commissionRate / 100);
  const afterCommission = afterDiscount + commission;

  const gstRate = baselineQuote.gst_rate || 0.05;
  const gst = afterCommission * gstRate;
  const grandTotal = afterCommission + gst;

  const result = {
    subtotal: Math.round(subtotal),
    discount: Math.round(discount),
    afterDiscount: Math.round(afterDiscount),
    commission: Math.round(commission),
    afterCommission: Math.round(afterCommission),
    gst: Math.round(gst),
    grandTotal: Math.round(grandTotal)
  };

  console.log(`💰 Final calculation summary:`, result);
  return result;
};

// Main Family Type Quote Calculation following Quote Generation Workflow
export const calculateFamilyTypeQuoteWorkflow = async (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData,
  hotelRows: HotelRow[]
): Promise<PackageCostResult> => {

  console.log(`\n🎯 === FAMILY TYPE QUOTE CALCULATION WORKFLOW ===`);
  console.log(`📊 Baseline Quote: ${baselineQuote.customer_name} - ₹${baselineQuote.total_cost}`);
  console.log(`👥 Family Type: ${familyType.family_type}`);
  console.log(`🏨 Hotel Rows: ${hotelRows.length}`);
  console.log(`📋 Quote Mapping: ${quoteMappingData.quote_name}`);

  // After Selecting Baseline Quote > Click Calculate Price
  // Check for Quote Mapping Fields ✅

  // Phase 1: Basic family Information Setup ✅
  // Fetch data from table family_type from TripXplo Quote DB ✅
  console.log(`✅ Phase 1: Family data loaded from database`);

  // Phase 2: Setup Family, Room Count & Cab Type
  const roomCalculation = calculateFamilyRoomAndCab(familyType);

  // Phase 3: Hotel Configuration
  const hotelCosts = calculateHotelCosts(familyType, roomCalculation, hotelRows, baselineQuote);

  // Phase 4: Additional Costs based on Quote Mapping
  const nights = hotelRows.reduce((sum, row) => Math.max(sum, row.stayNights), 1);
  const additionalCosts = calculateAdditionalCosts(familyType, baselineQuote, quoteMappingData, nights);
  const vehicleCosts = calculateVehicleCosts(familyType, baselineQuote, quoteMappingData);

  // Phase 5: Final Calculations
  const finalCalculation = calculateFinalCosts(hotelCosts, additionalCosts, vehicleCosts, baselineQuote);

  console.log(`\n🎉 === WORKFLOW COMPLETE ===`);
  console.log(`🏨 Hotel costs: ₹${hotelCosts.totalHotelCost}`);
  console.log(`🎯 Additional costs: ₹${additionalCosts.totalAdditionalCosts}`);
  console.log(`🚗 Vehicle costs: ₹${vehicleCosts.totalVehicleCost}`);
  console.log(`💰 GRAND TOTAL: ₹${finalCalculation.grandTotal}`);

  return {
    familyType,
    roomCalculation,
    hotelCosts,
    additionalCosts,
    vehicleCosts,
    finalCalculation
  };
};

// Create default Quote Mapping data if not available
export const createDefaultQuoteMappingData = (baselineQuote: BaselineQuote): QuoteMappingData => {
  console.log('🔧 Creating default Quote Mapping data from baseline quote');

  return {
    id: 'default-mapping',
    quote_id: baselineQuote.id,
    quote_name: baselineQuote.package_name,
    customer_name: baselineQuote.customer_name,
    destination: baselineQuote.destination,
    hotel_mappings: [{
      hotel_name: 'Default Hotel',
      extra_adult_cost: 1000,
      children_cost: 700,
      infant_cost: 0,
      grandparent_discount: 10
    }],
    vehicle_mappings: [
      { vehicle_type: 'Sedan/Dzire', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.0, max_capacity: 4, is_active: true },
      { vehicle_type: 'Toyota Innova AC', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.2, max_capacity: 7, is_active: true },
      { vehicle_type: 'SUV/Scorpio', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.25, max_capacity: 8, is_active: true }
    ],
    additional_costs: {
      meal_cost_per_person: Math.round(baselineQuote.total_cost * 0.15 / Math.max(baselineQuote.no_of_persons + baselineQuote.extra_adults, 1)), // 15% of total for meals
      ferry_cost: Math.round(baselineQuote.total_cost * 0.05), // 5% for ferry
      activity_cost_per_person: Math.round(baselineQuote.total_cost * 0.10 / Math.max(baselineQuote.no_of_persons + baselineQuote.extra_adults, 1)), // 10% for activities
      guide_cost_per_day: Math.round(baselineQuote.total_cost * 0.08 / 3), // 8% for guide over 3 days
      parking_toll_multiplier: 1.0
    }
  };
};

// Display Package Price for all families
export const calculateAllFamilyTypePrices = async (
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData | null,
  hotelRows: HotelRow[]
): Promise<PackageCostResult[]> => {

  console.log(`\n🎯 === CALCULATING ALL FAMILY TYPE PRICES ===`);

  // Use provided Quote Mapping or create default
  const finalQuoteMappingData = quoteMappingData || createDefaultQuoteMappingData(baselineQuote);

  if (!quoteMappingData) {
    console.log('⚠️ No Quote Mapping provided, using default mapping based on baseline quote');
  }

  // Phase 1: Fetch all family types
  const familyTypes = await fetchFamilyTypesFromDB();

  if (familyTypes.length === 0) {
    console.error('❌ No family types found in database');
    return [];
  }

  console.log(`✅ Found ${familyTypes.length} family types to calculate`);

  const results: PackageCostResult[] = [];

  // Calculate price for each family type
  for (const familyType of familyTypes) {
    try {
      const result = await calculateFamilyTypeQuoteWorkflow(
        familyType,
        baselineQuote,
        finalQuoteMappingData,
        hotelRows
      );
      results.push(result);
    } catch (error) {
      console.error(`❌ Error calculating price for ${familyType.family_type}:`, error);
    }
  }

  console.log(`\n🎉 === ALL FAMILY TYPE CALCULATIONS COMPLETE ===`);
  console.log(`✅ Successfully calculated ${results.length} family type prices`);

  return results;
};
